package schema

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"xorm.io/builder"
)

// SQLSchema 定义SQL查询的JSON Schema结构
type SQLSchema struct {
	Select     []string               `json:"select"`
	From       string                 `json:"from"`
	Where      map[string]interface{} `json:"where"`
	OrderBy    []string               `json:"order_by"`
	GroupBy    []string               `json:"group_by"`
	Having     map[string]interface{} `json:"having"`
	Limit      int                    `json:"limit"`
	Offset     int                    `json:"offset"`
	JoinTables []JoinTable            `json:"join_tables"`
}

// JoinTable 定义JOIN表结构
type JoinTable struct {
	Type      string                 `json:"type"` // LEFT, RIGHT, INNER
	Table     string                 `json:"table"`
	Condition map[string]interface{} `json:"condition"`
}

// AdvancedCondition 定义更复杂的条件结构
type AdvancedCondition struct {
	Operator string      `json:"operator"` // =, >, <, IN, LIKE 等
	Value    interface{} `json:"value"`
}

// WhereClause 支持复杂的WHERE条件
type WhereClause map[string]AdvancedCondition

// GenerateSQLFromJSON 从JSON字符串生成SQL
func GenerateSQLFromJSON(jsonStr string) (string, []interface{}, error) {
	var schema SQLSchema
	if err := json.Unmarshal([]byte(jsonStr), &schema); err != nil {
		return "", nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	return GenerateSQLFromSchema(schema)
}

// GenerateSQLFromSchema 从Schema结构生成SQL
func GenerateSQLFromSchema(schema SQLSchema) (string, []interface{}, error) {
	// 验证必要字段
	if len(schema.Select) == 0 {
		return "", nil, fmt.Errorf("select字段不能为空")
	}
	if schema.From == "" {
		return "", nil, fmt.Errorf("from字段不能为空")
	}

	// 构建查询
	query := builder.Dialect(builder.MYSQL).Select(schema.Select...)
	query = query.From(schema.From)

	// 处理 JOIN
	for _, join := range schema.JoinTables {
		switch join.Type {
		case "LEFT":
			query = query.LeftJoin(join.Table, builder.Eq(join.Condition))
		case "RIGHT":
			query = query.RightJoin(join.Table, builder.Eq(join.Condition))
		case "INNER":
			query = query.InnerJoin(join.Table, builder.Eq(join.Condition))
		default:
			return "", nil, fmt.Errorf("不支持的JOIN类型: %s", join.Type)
		}
	}

	// 处理 WHERE 条件
	if len(schema.Where) > 0 {
		query = query.Where(builder.Eq(schema.Where))
	}

	// 处理 GROUP BY
	if len(schema.GroupBy) > 0 {
		for _, field := range schema.GroupBy {
			query = query.GroupBy(field)
		}
	}

	// 处理 HAVING
	if len(schema.Having) > 0 {
		query = query.Having(builder.Eq(schema.Having))
	}

	// 处理 ORDER BY
	if len(schema.OrderBy) > 0 {
		for _, field := range schema.OrderBy {
			query = query.OrderBy(field)
		}
	}

	// 处理 LIMIT 和 OFFSET
	if schema.Limit > 0 {
		query = query.Limit(schema.Limit, schema.Offset)
	}

	// 生成 SQL
	return query.ToSQL()
}

// GenerateAdvancedSQL 处理高级条件查询
func GenerateAdvancedSQL(jsonStr string) (string, []interface{}, error) {
	var data struct {
		Select   []string                     `json:"select"`
		From     string                       `json:"from"`
		Where    map[string]interface{}       `json:"where"`
		AdvWhere map[string]AdvancedCondition `json:"adv_where"`
		OrderBy  []string                     `json:"order_by"`
		GroupBy  []string                     `json:"group_by"`
		Limit    int                          `json:"limit"`
		Offset   int                          `json:"offset"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 构建查询
	query := builder.Dialect(builder.MYSQL).Select(data.Select...).From(data.From)

	// 处理基本WHERE条件
	if len(data.Where) > 0 {
		query = query.Where(builder.Eq(data.Where))
	}

	// 处理高级WHERE条件
	if len(data.AdvWhere) > 0 {
		conds := make([]builder.Cond, 0, len(data.AdvWhere))

		for field, condition := range data.AdvWhere {
			switch condition.Operator {
			case "=":
				conds = append(conds, builder.Eq{field: condition.Value})
			case ">":
				conds = append(conds, builder.Gt{field: condition.Value})
			case "<":
				conds = append(conds, builder.Lt{field: condition.Value})
			case ">=":
				conds = append(conds, builder.Gte{field: condition.Value})
			case "<=":
				conds = append(conds, builder.Lte{field: condition.Value})
			case "IN":
				conds = append(conds, builder.In(field, condition.Value))
			case "LIKE":
				conds = append(conds, builder.Like{0: field, 1: condition.Value.(string)})
			case "IS NULL":
				conds = append(conds, builder.IsNull{field})
			default:
				return "", nil, fmt.Errorf("不支持的操作符: %s", condition.Operator)
			}
		}

		if len(conds) > 0 {
			query = query.Where(builder.And(conds...))
		}
	}

	// 处理其他条件
	if len(data.GroupBy) > 0 {
		for _, field := range data.GroupBy {
			query = query.GroupBy(field)
		}
	}

	if len(data.OrderBy) > 0 {
		for _, field := range data.OrderBy {
			query = query.OrderBy(field)
		}
	}

	if data.Limit > 0 {
		query = query.Limit(data.Limit, data.Offset)
	}

	return query.ToSQL()
}

// GenerateInsertSQL 从JSON生成INSERT SQL
func GenerateInsertSQL(jsonStr string) (string, []interface{}, error) {
	var data struct {
		Table  string                 `json:"table"`
		Values map[string]interface{} `json:"values"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	return builder.Dialect(builder.MYSQL).Insert(builder.Eq(data.Values)).Into(data.Table).ToSQL()
}

// GenerateUpdateSQL 从JSON生成UPDATE SQL
func GenerateUpdateSQL(jsonStr string) (string, []interface{}, error) {
	var data struct {
		Table  string                 `json:"table"`
		Values map[string]interface{} `json:"values"`
		Where  map[string]interface{} `json:"where"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	query := builder.Dialect(builder.MYSQL).Update(builder.Eq(data.Values)).From(data.Table)
	if len(data.Where) > 0 {
		query = query.Where(builder.Eq(data.Where))
	}

	return query.ToSQL()
}

// GenerateDeleteSQL 从JSON生成DELETE SQL
func GenerateDeleteSQL(jsonStr string) (string, []interface{}, error) {
	var data struct {
		Table string                 `json:"table"`
		Where map[string]interface{} `json:"where"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}

	query := builder.Dialect(builder.MYSQL).Delete(builder.Eq{}).From(data.Table)
	if len(data.Where) > 0 {
		query = query.Where(builder.Eq(data.Where))
	}

	return query.ToSQL()
}

// GenerateBatchInsertSQL 从JSON生成批量INSERT SQL
func GenerateBatchInsertSQL(jsonStr string) (string, []interface{}, error) {
	var data struct {
		Table   string          `json:"table"`
		Columns []string        `json:"columns"`
		Values  [][]interface{} `json:"values"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", nil, err
	}

	// 验证数据
	if data.Table == "" {
		return "", nil, fmt.Errorf("table字段不能为空")
	}
	if len(data.Columns) == 0 {
		return "", nil, fmt.Errorf("columns不能为空")
	}
	if len(data.Values) == 0 {
		return "", nil, fmt.Errorf("values不能为空")
	}

	// 检查每行的值数量是否与列数量匹配
	for i, row := range data.Values {
		if len(row) != len(data.Columns) {
			return "", nil, fmt.Errorf("第%d行的值数量(%d)与列数量(%d)不匹配", i+1, len(row), len(data.Columns))
		}
	}

	// xorm builder不直接支持批量插入，我们手动构建SQL
	// 构建INSERT部分
	sql := fmt.Sprintf("INSERT INTO %s (%s)", data.Table, strings.Join(data.Columns, ", "))

	// 构建VALUES部分
	valueStrings := make([]string, 0, len(data.Values))
	valueArgs := make([]interface{}, 0, len(data.Values)*len(data.Columns))

	// 为每行构建占位符并收集参数
	for _, row := range data.Values {
		placeholders := make([]string, len(data.Columns))
		for i := range data.Columns {
			placeholders[i] = "?"
			valueArgs = append(valueArgs, row[i])
		}
		valueStrings = append(valueStrings, fmt.Sprintf("(%s)", strings.Join(placeholders, ", ")))
	}

	// 组合成最终SQL
	sql += " VALUES " + strings.Join(valueStrings, ", ")

	return sql, valueArgs, nil
}

// 示例函数
func Example() {
	// 查询示例
	queryJSON := `{
		"select": ["user.id", "user.name", "user.age", "order.order_no"],
		"from": "user",
		"where": {
			"user.age": 25,
			"user.status": "active"
		},
		"join_tables": [
			{
				"type": "LEFT",
				"table": "order",
				"condition": {
					"user.id": "order.user_id"
				}
			}
		],
		"order_by": ["user.id DESC", "user.name ASC"],
		"group_by": ["user.age"],
		"having": {
			"COUNT(*)": 1
		},
		"limit": 10,
		"offset": 0
	}`

	sql, args, err := GenerateSQLFromJSON(queryJSON)
	if err != nil {
		log.Fatalf("生成查询SQL出错: %v", err)
	}
	fmt.Printf("查询SQL: %s\n参数: %v\n\n", sql, args)

	// 高级查询示例
	advQueryJSON := `{
		"select": ["id", "name", "age"],
		"from": "users",
		"adv_where": {
			"age": {
				"operator": ">",
				"value": 18
			},
			"status": {
				"operator": "IN",
				"value": ["active", "pending"]
			},
			"name": {
				"operator": "LIKE",
				"value": "%张%"
			}
		},
		"limit": 20
	}`

	sql, args, err = GenerateAdvancedSQL(advQueryJSON)
	if err != nil {
		log.Fatalf("生成高级查询SQL出错: %v", err)
	}
	fmt.Printf("高级查询SQL: %s\n参数: %v\n\n", sql, args)

	// 插入示例
	insertJSON := `{
		"table": "users",
		"values": {
			"name": "张三",
			"age": 25,
			"email": "<EMAIL>"
		}
	}`

	sql, args, err = GenerateInsertSQL(insertJSON)
	if err != nil {
		log.Fatalf("生成插入SQL出错: %v", err)
	}
	fmt.Printf("插入SQL: %s\n参数: %v\n\n", sql, args)

	// 更新示例
	updateJSON := `{
		"table": "users",
		"values": {
			"age": 26,
			"status": "inactive"
		},
		"where": {
			"id": 1
		}
	}`

	sql, args, err = GenerateUpdateSQL(updateJSON)
	if err != nil {
		log.Fatalf("生成更新SQL出错: %v", err)
	}
	fmt.Printf("更新SQL: %s\n参数: %v\n\n", sql, args)

	// 删除示例
	deleteJSON := `{
		"table": "users",
		"where": {
			"id": 1
		}
	}`

	sql, args, err = GenerateDeleteSQL(deleteJSON)
	if err != nil {
		log.Fatalf("生成删除SQL出错: %v", err)
	}
	fmt.Printf("删除SQL: %s\n参数: %v\n\n", sql, args)

	// 批量插入示例
	batchInsertJSON := `{
		"table": "users",
		"columns": ["name", "age", "email"],
		"values": [
			["张三", 25, "<EMAIL>"],
			["李四", 30, "<EMAIL>"],
			["王五", 35, "<EMAIL>"]
		]
	}`

	sql, args, err = GenerateBatchInsertSQL(batchInsertJSON)
	if err != nil {
		log.Fatalf("生成批量插入SQL出错: %v", err)
	}
	fmt.Printf("批量插入SQL: %s\n参数: %v\n", sql, args)
}
