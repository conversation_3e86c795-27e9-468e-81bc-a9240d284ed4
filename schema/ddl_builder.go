package schema

import (
	"encoding/json"
	"fmt"
	"strings"
)

// TableSchema 定义表结构的JSON Schema
type TableSchema struct {
	Name        string            `json:"name"`
	Columns     map[string]string `json:"columns"`
	Constraints map[string]string `json:"constraints"`
	Indexes     map[string]string `json:"indexes"`
	Engine      string            `json:"engine"`
	Charset     string            `json:"charset"`
	Collate     string            `json:"collate"`
	Comment     string            `json:"comment"`
	IfNotExists bool              `json:"if_not_exists"`
}

// IndexSchema 定义索引的JSON Schema
type IndexSchema struct {
	Name        string   `json:"name"`
	Table       string   `json:"table"`
	Columns     []string `json:"columns"`
	Unique      bool     `json:"unique"`
	IfNotExists bool     `json:"if_not_exists"`
}

// AlterTableSchema 定义修改表的JSON Schema
type AlterTableSchema struct {
	Name            string            `json:"name"`
	AddColumns      map[string]string `json:"add_columns"`
	DropColumns     []string          `json:"drop_columns"`
	ModifyColumns   map[string]string `json:"modify_columns"`
	RenameColumns   map[string]string `json:"rename_columns"`
	AddConstraints  map[string]string `json:"add_constraints"`
	DropConstraints []string          `json:"drop_constraints"`
}

// GenerateCreateTableSQL 从JSON生成建表SQL
func GenerateCreateTableSQL(jsonStr string) (string, error) {
	var schema TableSchema
	if err := json.Unmarshal([]byte(jsonStr), &schema); err != nil {
		return "", err
	}

	if schema.Name == "" {
		return "", fmt.Errorf("表名不能为空")
	}
	if len(schema.Columns) == 0 {
		return "", fmt.Errorf("列定义不能为空")
	}

	// 构建CREATE TABLE语句
	sql := "CREATE TABLE "
	if schema.IfNotExists {
		sql += "IF NOT EXISTS "
	}
	sql += schema.Name + " (\n"

	// 添加列定义
	columns := []string{}
	for colName, colDef := range schema.Columns {
		columns = append(columns, "\t"+colName+" "+colDef)
	}

	// 添加约束
	for constName, constDef := range schema.Constraints {
		columns = append(columns, "\tCONSTRAINT "+constName+" "+constDef)
	}

	// 合并所有列和约束
	sql += strings.Join(columns, ",\n")
	sql += "\n)"

	// 添加表选项
	options := []string{}
	if schema.Engine != "" {
		options = append(options, "ENGINE="+schema.Engine)
	}
	if schema.Charset != "" {
		options = append(options, "DEFAULT CHARSET="+schema.Charset)
	}
	if schema.Collate != "" {
		options = append(options, "COLLATE="+schema.Collate)
	}
	if schema.Comment != "" {
		options = append(options, "COMMENT='"+schema.Comment+"'")
	}

	if len(options) > 0 {
		sql += "\n" + strings.Join(options, " ")
	}

	return sql + ";", nil
}

// GenerateCreateIndexSQL 从JSON生成创建索引SQL
func GenerateCreateIndexSQL(jsonStr string) (string, error) {
	var schema IndexSchema
	if err := json.Unmarshal([]byte(jsonStr), &schema); err != nil {
		return "", err
	}

	if schema.Name == "" {
		return "", fmt.Errorf("索引名不能为空")
	}
	if schema.Table == "" {
		return "", fmt.Errorf("表名不能为空")
	}
	if len(schema.Columns) == 0 {
		return "", fmt.Errorf("索引列不能为空")
	}

	// 构建CREATE INDEX语句
	sql := "CREATE "
	if schema.Unique {
		sql += "UNIQUE "
	}
	sql += "INDEX "
	if schema.IfNotExists {
		sql += "IF NOT EXISTS "
	}
	sql += schema.Name + " ON " + schema.Table
	sql += " (" + strings.Join(schema.Columns, ", ") + ");"

	return sql, nil
}

// GenerateAlterTableSQL 从JSON生成修改表SQL
func GenerateAlterTableSQL(jsonStr string) ([]string, error) {
	var schema AlterTableSchema
	if err := json.Unmarshal([]byte(jsonStr), &schema); err != nil {
		return nil, err
	}

	if schema.Name == "" {
		return nil, fmt.Errorf("表名不能为空")
	}

	// 可能需要多条ALTER语句
	results := []string{}

	// 添加列
	if len(schema.AddColumns) > 0 {
		addCols := []string{}
		for colName, colDef := range schema.AddColumns {
			addCols = append(addCols, "ADD COLUMN "+colName+" "+colDef)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(addCols, ",\n\t")+";")
	}

	// 删除列
	if len(schema.DropColumns) > 0 {
		dropCols := []string{}
		for _, colName := range schema.DropColumns {
			dropCols = append(dropCols, "DROP COLUMN "+colName)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(dropCols, ",\n\t")+";")
	}

	// 修改列
	if len(schema.ModifyColumns) > 0 {
		modifyCols := []string{}
		for colName, colDef := range schema.ModifyColumns {
			modifyCols = append(modifyCols, "MODIFY COLUMN "+colName+" "+colDef)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(modifyCols, ",\n\t")+";")
	}

	// 重命名列
	if len(schema.RenameColumns) > 0 {
		renameCols := []string{}
		for oldName, newName := range schema.RenameColumns {
			renameCols = append(renameCols, "RENAME COLUMN "+oldName+" TO "+newName)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(renameCols, ",\n\t")+";")
	}

	// 添加约束
	if len(schema.AddConstraints) > 0 {
		addConsts := []string{}
		for constName, constDef := range schema.AddConstraints {
			addConsts = append(addConsts, "ADD CONSTRAINT "+constName+" "+constDef)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(addConsts, ",\n\t")+";")
	}

	// 删除约束
	if len(schema.DropConstraints) > 0 {
		dropConsts := []string{}
		for _, constName := range schema.DropConstraints {
			dropConsts = append(dropConsts, "DROP CONSTRAINT "+constName)
		}
		results = append(results, "ALTER TABLE "+schema.Name+" \n\t"+strings.Join(dropConsts, ",\n\t")+";")
	}

	return results, nil
}

// DDLExample 演示DDL操作
func DDLExample() {
	// 创建表示例
	createTableJSON := `{
		"name": "users",
		"columns": {
			"id": "bigint(20) NOT NULL AUTO_INCREMENT",
			"name": "varchar(255) NOT NULL",
			"email": "varchar(255) NOT NULL",
			"age": "int(11)",
			"status": "enum('active','inactive') DEFAULT 'active'",
			"created_at": "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"updated_at": "timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP"
		},
		"constraints": {
			"PRIMARY": "PRIMARY KEY (id)",
			"uk_email": "UNIQUE KEY (email)"
		},
		"engine": "InnoDB",
		"charset": "utf8mb4",
		"collate": "utf8mb4_unicode_ci",
		"comment": "用户表",
		"if_not_exists": true
	}`

	sql, err := GenerateCreateTableSQL(createTableJSON)
	if err != nil {
		fmt.Printf("生成创建表SQL出错: %v\n", err)
		return
	}
	fmt.Printf("创建表SQL:\n%s\n\n", sql)

	// 创建索引示例
	createIndexJSON := `{
		"name": "idx_name_age",
		"table": "users",
		"columns": ["name", "age"],
		"unique": true,
		"if_not_exists": true
	}`

	sql, err = GenerateCreateIndexSQL(createIndexJSON)
	if err != nil {
		fmt.Printf("生成创建索引SQL出错: %v\n", err)
		return
	}
	fmt.Printf("创建索引SQL:\n%s\n\n", sql)

	// 修改表示例
	alterTableJSON := `{
		"name": "users",
		"add_columns": {
			"phone": "varchar(20) DEFAULT NULL COMMENT '手机号'",
			"address": "varchar(255) DEFAULT NULL COMMENT '地址'"
		},
		"drop_columns": ["age"],
		"modify_columns": {
			"name": "varchar(100) NOT NULL COMMENT '用户名'"
		},
		"rename_columns": {
			"email": "mail"
		},
		"add_constraints": {
			"idx_status": "INDEX (status)"
		},
		"drop_constraints": ["uk_email"]
	}`

	statements, err := GenerateAlterTableSQL(alterTableJSON)
	if err != nil {
		fmt.Printf("生成修改表SQL出错: %v\n", err)
		return
	}
	fmt.Println("修改表SQL:")
	for i, stmt := range statements {
		fmt.Printf("%d. %s\n", i+1, stmt)
	}
}
