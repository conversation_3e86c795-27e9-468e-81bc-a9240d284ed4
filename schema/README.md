# XORM Builder JSON工具

这个项目提供了一种基于JSON定义SQL查询的方法，通过xorm/builder库生成相应的SQL语句。

## 功能特点

- 支持通过JSON定义SELECT、INSERT、UPDATE、DELETE操作
- 支持复杂的JOIN操作
- 支持高级WHERE条件（>, <, IN, LIKE等）
- 支持GROUP BY, HAVING, ORDER BY, LIMIT, OFFSET等SQL特性
- 参数化查询，防止SQL注入

## 安装依赖

```bash
go get xorm.io/builder
```

## 使用方法

### 基本查询

```go
jsonStr := `{
    "select": ["id", "name", "age"],
    "from": "users",
    "where": {
        "status": "active"
    },
    "limit": 10
}`

sql, args, err := GenerateSQLFromJSON(jsonStr)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("SQL: %s\nArgs: %v\n", sql, args)
```

### 高级查询

```go
jsonStr := `{
    "select": ["id", "name", "age"],
    "from": "users",
    "adv_where": {
        "age": {
            "operator": ">",
            "value": 18
        },
        "status": {
            "operator": "IN",
            "value": ["active", "pending"]
        },
        "name": {
            "operator": "LIKE",
            "value": "%张%"
        }
    }
}`

sql, args, err := GenerateAdvancedSQL(jsonStr)
```

### 联表查询

```go
jsonStr := `{
    "select": ["users.id", "users.name", "orders.order_no"],
    "from": "users",
    "join_tables": [
        {
            "type": "LEFT",
            "table": "orders",
            "condition": {
                "users.id": "orders.user_id"
            }
        }
    ],
    "where": {
        "users.status": "active"
    }
}`
```

### 插入记录

```go
jsonStr := `{
    "table": "users",
    "values": {
        "name": "张三",
        "age": 25,
        "email": "<EMAIL>"
    }
}`

sql, args, err := GenerateInsertSQL(jsonStr)
```

### 更新记录

```go
jsonStr := `{
    "table": "users",
    "values": {
        "age": 26,
        "status": "inactive"
    },
    "where": {
        "id": 1
    }
}`

sql, args, err := GenerateUpdateSQL(jsonStr)
```

### 删除记录

```go
jsonStr := `{
    "table": "users",
    "where": {
        "id": 1
    }
}`

sql, args, err := GenerateDeleteSQL(jsonStr)
```

## 注意事项

1. 确保JSON格式正确
2. 查询表名和字段名不做安全检查，使用前应验证输入
3. 建议对生成的SQL进行日志记录和监控
4. 对于非常复杂的查询，可能需要直接使用原生SQL

## 许可证

MIT
