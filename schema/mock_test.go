package schema

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"xorm.io/builder"
)

// 测试辅助函数

type builderMock struct {
	sql  string
	args []interface{}
	err  error
}

func (b *builderMock) ToSQL() (string, []interface{}, error) {
	return b.sql, b.args, b.err
}

// 测试builder.Eq是否按预期工作
func TestBuilderEq(t *testing.T) {
	conditions := builder.Eq{"name": "张三", "age": 25}
	assert.Equal(t, "张三", conditions["name"])
	assert.Equal(t, 25, conditions["age"])
}

// 测试builder.And是否按预期工作
func TestBuilderAnd(t *testing.T) {
	cond1 := builder.Eq{"name": "张三"}
	cond2 := builder.Gt{"age": 18}
	andCond := builder.And(cond1, cond2)

	// 检查AndCond是否组合了两个条件
	assert.NotNil(t, andCond)
}

// 测试builder.In是否按预期工作
func TestBuilderIn(t *testing.T) {
	inCond := builder.In("status", []string{"active", "pending"})
	assert.NotNil(t, inCond)
}

// 测试builder.Like是否按预期工作
func TestBuilderLike(t *testing.T) {
	likeCond := builder.Like{0: "name", 1: "%张%"}
	assert.Equal(t, "name", likeCond[0])
	assert.Equal(t, "%张%", likeCond[1])
}
