package schema

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenerateCreateTableSQL(t *testing.T) {
	tests := []struct {
		name     string
		jsonStr  string
		expected string
		hasError bool
	}{
		{
			name: "完整的表定义",
			jsonStr: `{
				"name": "users",
				"columns": {
					"id": "bigint(20) NOT NULL AUTO_INCREMENT",
					"name": "varchar(255) NOT NULL",
					"email": "varchar(255) NOT NULL"
				},
				"constraints": {
					"PRIMARY": "PRIMARY KEY (id)",
					"uk_email": "UNIQUE KEY (email)"
				},
				"engine": "InnoDB",
				"charset": "utf8mb4",
				"collate": "utf8mb4_unicode_ci",
				"comment": "用户表",
				"if_not_exists": true
			}`,
			expected: "CREATE TABLE IF NOT EXISTS users (\n\tid bigint(20) NOT NULL AUTO_INCREMENT,\n\tname varchar(255) NOT NULL,\n\temail varchar(255) NOT NULL,\n\tCONSTRAINT PRIMARY PRIMARY KEY (id),\n\tCONSTRAINT uk_email UNIQUE KEY (email)\n)\nENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';",
			hasError: false,
		},
		{
			name: "最小表定义",
			jsonStr: `{
				"name": "simple",
				"columns": {
					"id": "int",
					"name": "varchar(100)"
				}
			}`,
			expected: "CREATE TABLE simple (\n\tid int,\n\tname varchar(100)\n);",
			hasError: false,
		},
		{
			name: "表名为空",
			jsonStr: `{
				"name": "",
				"columns": {
					"id": "int"
				}
			}`,
			expected: "",
			hasError: true,
		},
		{
			name: "列为空",
			jsonStr: `{
				"name": "empty_columns",
				"columns": {}
			}`,
			expected: "",
			hasError: true,
		},
		{
			name:     "无效的JSON",
			jsonStr:  `{"name": "invalid", "columns": {"id": "int",}`,
			expected: "",
			hasError: true,
		},
		{
			name: "只有约束没有列",
			jsonStr: `{
				"name": "only_constraints",
				"columns": {"id": "int"},
				"constraints": {
					"PRIMARY": "PRIMARY KEY (id)"
				}
			}`,
			expected: "CREATE TABLE only_constraints (\n\tid int,\n\tCONSTRAINT PRIMARY PRIMARY KEY (id)\n);",
			hasError: false,
		},
		{
			name: "只有引擎选项",
			jsonStr: `{
				"name": "engine_only",
				"columns": {"id": "int"},
				"engine": "MyISAM"
			}`,
			expected: "CREATE TABLE engine_only (\n\tid int\n)\nENGINE=MyISAM;",
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sql, err := GenerateCreateTableSQL(tt.jsonStr)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, sql)
			}
		})
	}
}

func TestGenerateCreateIndexSQL(t *testing.T) {
	tests := []struct {
		name     string
		jsonStr  string
		expected string
		hasError bool
	}{
		{
			name: "普通索引",
			jsonStr: `{
				"name": "idx_name",
				"table": "users",
				"columns": ["name"]
			}`,
			expected: "CREATE INDEX idx_name ON users (name);",
			hasError: false,
		},
		{
			name: "唯一索引且IF NOT EXISTS",
			jsonStr: `{
				"name": "idx_email",
				"table": "users",
				"columns": ["email"],
				"unique": true,
				"if_not_exists": true
			}`,
			expected: "CREATE UNIQUE INDEX IF NOT EXISTS idx_email ON users (email);",
			hasError: false,
		},
		{
			name: "多列索引",
			jsonStr: `{
				"name": "idx_name_age",
				"table": "users",
				"columns": ["name", "age"]
			}`,
			expected: "CREATE INDEX idx_name_age ON users (name, age);",
			hasError: false,
		},
		{
			name: "索引名为空",
			jsonStr: `{
				"name": "",
				"table": "users",
				"columns": ["name"]
			}`,
			expected: "",
			hasError: true,
		},
		{
			name: "表名为空",
			jsonStr: `{
				"name": "idx_name",
				"table": "",
				"columns": ["name"]
			}`,
			expected: "",
			hasError: true,
		},
		{
			name: "列为空",
			jsonStr: `{
				"name": "idx_name",
				"table": "users",
				"columns": []
			}`,
			expected: "",
			hasError: true,
		},
		{
			name:     "无效的JSON",
			jsonStr:  `{"name": "idx_invalid", "table": "users", "columns": ["name",]}`,
			expected: "",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sql, err := GenerateCreateIndexSQL(tt.jsonStr)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, sql)
			}
		})
	}
}

func TestGenerateAlterTableSQL(t *testing.T) {
	tests := []struct {
		name           string
		jsonStr        string
		expectedResult []string
		hasError       bool
	}{
		{
			name: "添加列",
			jsonStr: `{
				"name": "users",
				"add_columns": {
					"phone": "varchar(20) DEFAULT NULL",
					"address": "varchar(255) DEFAULT NULL"
				}
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tADD COLUMN phone varchar(20) DEFAULT NULL,\n\tADD COLUMN address varchar(255) DEFAULT NULL;",
			},
			hasError: false,
		},
		{
			name: "删除列",
			jsonStr: `{
				"name": "users",
				"drop_columns": ["phone", "address"]
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tDROP COLUMN phone,\n\tDROP COLUMN address;",
			},
			hasError: false,
		},
		{
			name: "修改列",
			jsonStr: `{
				"name": "users",
				"modify_columns": {
					"name": "varchar(100) NOT NULL",
					"email": "varchar(200) NOT NULL UNIQUE"
				}
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tMODIFY COLUMN name varchar(100) NOT NULL,\n\tMODIFY COLUMN email varchar(200) NOT NULL UNIQUE;",
			},
			hasError: false,
		},
		{
			name: "重命名列",
			jsonStr: `{
				"name": "users",
				"rename_columns": {
					"email": "mail",
					"phone": "telephone"
				}
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tRENAME COLUMN email TO mail,\n\tRENAME COLUMN phone TO telephone;",
			},
			hasError: false,
		},
		{
			name: "添加约束",
			jsonStr: `{
				"name": "users",
				"add_constraints": {
					"idx_name": "INDEX (name)",
					"idx_email": "UNIQUE (email)"
				}
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tADD CONSTRAINT idx_name INDEX (name),\n\tADD CONSTRAINT idx_email UNIQUE (email);",
			},
			hasError: false,
		},
		{
			name: "删除约束",
			jsonStr: `{
				"name": "users",
				"drop_constraints": ["idx_name", "idx_email"]
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tDROP CONSTRAINT idx_name,\n\tDROP CONSTRAINT idx_email;",
			},
			hasError: false,
		},
		{
			name: "多种操作",
			jsonStr: `{
				"name": "users",
				"add_columns": {
					"phone": "varchar(20) DEFAULT NULL"
				},
				"drop_columns": ["address"],
				"modify_columns": {
					"name": "varchar(100) NOT NULL"
				},
				"rename_columns": {
					"email": "mail"
				},
				"add_constraints": {
					"idx_name": "INDEX (name)"
				},
				"drop_constraints": ["idx_old"]
			}`,
			expectedResult: []string{
				"ALTER TABLE users \n\tADD COLUMN phone varchar(20) DEFAULT NULL;",
				"ALTER TABLE users \n\tDROP COLUMN address;",
				"ALTER TABLE users \n\tMODIFY COLUMN name varchar(100) NOT NULL;",
				"ALTER TABLE users \n\tRENAME COLUMN email TO mail;",
				"ALTER TABLE users \n\tADD CONSTRAINT idx_name INDEX (name);",
				"ALTER TABLE users \n\tDROP CONSTRAINT idx_old;",
			},
			hasError: false,
		},
		{
			name: "表名为空",
			jsonStr: `{
				"name": "",
				"add_columns": {
					"phone": "varchar(20) DEFAULT NULL"
				}
			}`,
			expectedResult: nil,
			hasError:       true,
		},
		{
			name:           "无效的JSON",
			jsonStr:        `{"name": "users", "add_columns": {"phone": "varchar(20)",}`,
			expectedResult: nil,
			hasError:       true,
		},
		{
			name: "空操作",
			jsonStr: `{
				"name": "users"
			}`,
			expectedResult: []string{},
			hasError:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sqls, err := GenerateAlterTableSQL(tt.jsonStr)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.expectedResult), len(sqls))
				for i, expectedSQL := range tt.expectedResult {
					if i < len(sqls) {
						assert.Equal(t, expectedSQL, sqls[i])
					}
				}
			}
		})
	}
}

// 测试TableSchema结构体的序列化和反序列化
func TestTableSchemaJSONMarshaling(t *testing.T) {
	schema := TableSchema{
		Name: "test_table",
		Columns: map[string]string{
			"id":   "int NOT NULL",
			"name": "varchar(100)",
		},
		Constraints: map[string]string{
			"pk_id": "PRIMARY KEY (id)",
		},
		Engine:      "InnoDB",
		Charset:     "utf8mb4",
		Collate:     "utf8mb4_general_ci",
		Comment:     "测试表",
		IfNotExists: true,
	}

	// 序列化为JSON
	jsonBytes, err := json.Marshal(schema)
	assert.NoError(t, err)

	// 反序列化回来
	var newSchema TableSchema
	err = json.Unmarshal(jsonBytes, &newSchema)
	assert.NoError(t, err)

	// 确保反序列化后的结构与原结构相同
	assert.Equal(t, schema.Name, newSchema.Name)
	assert.Equal(t, schema.Columns, newSchema.Columns)
	assert.Equal(t, schema.Constraints, newSchema.Constraints)
	assert.Equal(t, schema.Engine, newSchema.Engine)
	assert.Equal(t, schema.Charset, newSchema.Charset)
	assert.Equal(t, schema.Collate, newSchema.Collate)
	assert.Equal(t, schema.Comment, newSchema.Comment)
	assert.Equal(t, schema.IfNotExists, newSchema.IfNotExists)
}

// 测试IndexSchema结构体的序列化和反序列化
func TestIndexSchemaJSONMarshaling(t *testing.T) {
	schema := IndexSchema{
		Name:        "idx_test",
		Table:       "test_table",
		Columns:     []string{"name", "age"},
		Unique:      true,
		IfNotExists: true,
	}

	// 序列化为JSON
	jsonBytes, err := json.Marshal(schema)
	assert.NoError(t, err)

	// 反序列化回来
	var newSchema IndexSchema
	err = json.Unmarshal(jsonBytes, &newSchema)
	assert.NoError(t, err)

	// 确保反序列化后的结构与原结构相同
	assert.Equal(t, schema.Name, newSchema.Name)
	assert.Equal(t, schema.Table, newSchema.Table)
	assert.Equal(t, schema.Columns, newSchema.Columns)
	assert.Equal(t, schema.Unique, newSchema.Unique)
	assert.Equal(t, schema.IfNotExists, newSchema.IfNotExists)
}

// 测试AlterTableSchema结构体的序列化和反序列化
func TestAlterTableSchemaJSONMarshaling(t *testing.T) {
	schema := AlterTableSchema{
		Name: "test_table",
		AddColumns: map[string]string{
			"email": "varchar(255) NOT NULL",
		},
		DropColumns: []string{"old_field"},
		ModifyColumns: map[string]string{
			"name": "varchar(200) NOT NULL",
		},
		RenameColumns: map[string]string{
			"old_name": "new_name",
		},
		AddConstraints: map[string]string{
			"idx_email": "UNIQUE (email)",
		},
		DropConstraints: []string{"old_constraint"},
	}

	// 序列化为JSON
	jsonBytes, err := json.Marshal(schema)
	assert.NoError(t, err)

	// 反序列化回来
	var newSchema AlterTableSchema
	err = json.Unmarshal(jsonBytes, &newSchema)
	assert.NoError(t, err)

	// 确保反序列化后的结构与原结构相同
	assert.Equal(t, schema.Name, newSchema.Name)
	assert.Equal(t, schema.AddColumns, newSchema.AddColumns)
	assert.Equal(t, schema.DropColumns, newSchema.DropColumns)
	assert.Equal(t, schema.ModifyColumns, newSchema.ModifyColumns)
	assert.Equal(t, schema.RenameColumns, newSchema.RenameColumns)
	assert.Equal(t, schema.AddConstraints, newSchema.AddConstraints)
	assert.Equal(t, schema.DropConstraints, newSchema.DropConstraints)
}

// 测试DDLExample函数是否正常执行
func TestDDLExample(t *testing.T) {
	// 这个测试只是确保函数执行不会崩溃
	DDLExample()
	// 由于该函数主要是打印输出，所以不进行实际的输出检查
	assert.True(t, true) // 如果函数执行完毕没有崩溃，则测试通过
}
