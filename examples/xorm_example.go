package main

import (
	"fmt"
	"log"

	"xorm-json-builder/schema"
)

func main() {
	// 查询示例
	queryJSON := `{
		"select": ["user.id", "user.name", "user.age", "order.order_no"],
		"from": "user",
		"where": {
			"user.age": 25,
			"user.status": "active"
		},
		"join_tables": [
			{
				"type": "LEFT",
				"table": "order",
				"condition": {
					"user.id": "order.user_id"
				}
			}
		],
		"order_by": ["user.id DESC", "user.name ASC"],
		"group_by": ["user.age"],
		"having": {
			"COUNT(*)": 1
		},
		"limit": 10,
		"offset": 0
	}`

	sql, args, err := schema.GenerateSQLFromJSON(queryJSON)
	if err != nil {
		log.Fatalf("生成查询SQL出错: %v", err)
	}
	fmt.Printf("查询SQL: %s\n参数: %v\n\n", sql, args)

	// 高级查询示例
	advQueryJSON := `{
		"select": ["id", "name", "age"],
		"from": "users",
		"adv_where": {
			"age": {
				"operator": ">",
				"value": 18
			},
			"status": {
				"operator": "IN",
				"value": ["active", "pending"]
			},
			"name": {
				"operator": "LIKE",
				"value": "%张%"
			}
		},
		"limit": 20
	}`

	sql, args, err = schema.GenerateAdvancedSQL(advQueryJSON)
	if err != nil {
		log.Fatalf("生成高级查询SQL出错: %v", err)
	}
	fmt.Printf("高级查询SQL: %s\n参数: %v\n\n", sql, args)

	// 插入示例
	insertJSON := `{
		"table": "users",
		"values": {
			"name": "张三",
			"age": 25,
			"email": "<EMAIL>"
		}
	}`

	sql, args, err = schema.GenerateInsertSQL(insertJSON)
	if err != nil {
		log.Fatalf("生成插入SQL出错: %v", err)
	}
	fmt.Printf("插入SQL: %s\n参数: %v\n\n", sql, args)

	// 更新示例
	updateJSON := `{
		"table": "users",
		"values": {
			"age": 26,
			"status": "inactive"
		},
		"where": {
			"id": 1
		}
	}`

	sql, args, err = schema.GenerateUpdateSQL(updateJSON)
	if err != nil {
		log.Fatalf("生成更新SQL出错: %v", err)
	}
	fmt.Printf("更新SQL: %s\n参数: %v\n\n", sql, args)

	// 删除示例
	deleteJSON := `{
		"table": "users",
		"where": {
			"id": 1
		}
	}`

	sql, args, err = schema.GenerateDeleteSQL(deleteJSON)
	if err != nil {
		log.Fatalf("生成删除SQL出错: %v", err)
	}
	fmt.Printf("删除SQL: %s\n参数: %v\n", sql, args)
}
