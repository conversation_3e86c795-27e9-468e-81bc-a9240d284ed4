package main

import (
	"fmt"
	"log"

	"xorm-json-builder/schema"
)

func main() {
	// 创建表示例
	createTableJSON := `{
		"name": "products",
		"columns": {
			"id": "bigint(20) NOT NULL AUTO_INCREMENT",
			"name": "varchar(255) NOT NULL COMMENT '商品名称'",
			"price": "decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格'",
			"category_id": "int(11) NOT NULL COMMENT '分类ID'",
			"stock": "int(11) NOT NULL DEFAULT 0 COMMENT '库存'",
			"description": "text COMMENT '商品描述'",
			"status": "tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:1正常,0下架'",
			"created_at": "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
			"updated_at": "timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'"
		},
		"constraints": {
			"PRIMARY": "PRIMARY KEY (id)",
			"idx_category": "KEY (category_id)",
			"idx_status": "KEY (status)"
		},
		"engine": "InnoDB",
		"charset": "utf8mb4",
		"collate": "utf8mb4_unicode_ci",
		"comment": "商品表",
		"if_not_exists": true
	}`

	sql, err := schema.GenerateCreateTableSQL(createTableJSON)
	if err != nil {
		log.Fatalf("生成创建表SQL出错: %v\n", err)
	}
	fmt.Printf("创建表SQL:\n%s\n\n", sql)

	// 创建多个表示例
	tables := []string{
		`{
			"name": "categories",
			"columns": {
				"id": "int(11) NOT NULL AUTO_INCREMENT",
				"name": "varchar(100) NOT NULL COMMENT '分类名称'",
				"parent_id": "int(11) DEFAULT NULL COMMENT '父分类ID'",
				"level": "tinyint(1) NOT NULL DEFAULT 1 COMMENT '分类层级'",
				"sort": "int(11) NOT NULL DEFAULT 0 COMMENT '排序值'",
				"status": "tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态'"
			},
			"constraints": {
				"PRIMARY": "PRIMARY KEY (id)",
				"idx_parent": "KEY (parent_id)"
			},
			"engine": "InnoDB",
			"charset": "utf8mb4",
			"comment": "商品分类表",
			"if_not_exists": true
		}`,
		`{
			"name": "orders",
			"columns": {
				"id": "bigint(20) NOT NULL AUTO_INCREMENT",
				"order_no": "varchar(64) NOT NULL COMMENT '订单号'",
				"user_id": "bigint(20) NOT NULL COMMENT '用户ID'",
				"total_amount": "decimal(10,2) NOT NULL COMMENT '订单总金额'",
				"status": "tinyint(2) NOT NULL DEFAULT 1 COMMENT '订单状态'",
				"payment_time": "datetime DEFAULT NULL COMMENT '支付时间'",
				"shipping_time": "datetime DEFAULT NULL COMMENT '发货时间'",
				"created_at": "timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP",
				"updated_at": "timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP"
			},
			"constraints": {
				"PRIMARY": "PRIMARY KEY (id)",
				"uk_order_no": "UNIQUE KEY (order_no)",
				"idx_user": "KEY (user_id)",
				"idx_status": "KEY (status)"
			},
			"engine": "InnoDB",
			"charset": "utf8mb4",
			"comment": "订单表",
			"if_not_exists": true
		}`
	}

	fmt.Println("创建多个表SQL:")
	for i, tableJSON := range tables {
		sql, err := schema.GenerateCreateTableSQL(tableJSON)
		if err != nil {
			log.Printf("生成表%d的SQL出错: %v\n", i+1, err)
			continue
		}
		fmt.Printf("\n表%d:\n%s\n", i+1, sql)
	}
	fmt.Println()

	// 创建索引示例
	indexes := []string{
		`{
			"name": "idx_products_name",
			"table": "products",
			"columns": ["name(191)"],
			"if_not_exists": true
		}`,
		`{
			"name": "idx_products_price",
			"table": "products",
			"columns": ["price"],
			"if_not_exists": true
		}`,
		`{
			"name": "idx_orders_created",
			"table": "orders",
			"columns": ["created_at"],
			"if_not_exists": true
		}`
	}

	fmt.Println("创建索引SQL:")
	for i, indexJSON := range indexes {
		sql, err := schema.GenerateCreateIndexSQL(indexJSON)
		if err != nil {
			log.Printf("生成索引%d的SQL出错: %v\n", i+1, err)
			continue
		}
		fmt.Printf("%d. %s\n", i+1, sql)
	}
	fmt.Println()

	// 修改表示例
	alterTableJSON := `{
		"name": "products",
		"add_columns": {
			"brand_id": "int(11) DEFAULT NULL COMMENT '品牌ID'",
			"is_featured": "tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐'"
		},
		"modify_columns": {
			"name": "varchar(200) NOT NULL COMMENT '商品名称(修改后)'",
			"price": "decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '价格(修改后)'"
		},
		"add_constraints": {
			"idx_brand": "KEY (brand_id)",
			"idx_featured": "KEY (is_featured)"
		}
	}`

	statements, err := schema.GenerateAlterTableSQL(alterTableJSON)
	if err != nil {
		log.Fatalf("生成修改表SQL出错: %v\n", err)
	}
	fmt.Println("修改表SQL:")
	for i, stmt := range statements {
		fmt.Printf("%d. %s\n", i+1, stmt)
	}
}
